defimpl Drops.Schema.Inference, for: Atom do
  @moduledoc """
  Schema inference implementation for Ecto schema modules.

  This implementation handles the inference of schemas from Ecto schema modules
  by introspecting their field definitions and converting them to Drops schema format.

  ## Examples

      # Given an Ecto schema:
      defmodule User do
        use Ecto.Schema

        schema "users" do
          field(:name, :string)
          field(:email, :string)
          field(:age, :integer)
          timestamps()
        end
      end

      # Inference will produce:
      %{
        optional(:name) => string(),
        optional(:email) => string(),
        optional(:age) => integer()
      }

  ## Field Filtering

  By default, the following fields are excluded from inference:
  - `:id` - Primary key field
  - `:inserted_at` - Timestamp field
  - `:updated_at` - Timestamp field

  This can be customized via options.

  ## Field Presence

  By default, all fields are marked as optional. This can be customized
  by providing field presence configuration in the options.
  """

  import Drops.Type.DSL

  @doc """
  Infer schema from an Ecto schema module.

  ## Parameters

  - `module` - An Ecto schema module
  - `opts` - Options for inference:
    - `:exclude_fields` - List of field names to exclude (default: `[:id, :inserted_at, :updated_at]`)
    - `:field_presence` - Map of field names to presence (`:required` or `:optional`)
    - `:default_presence` - Default presence for fields (default: `:optional`)

  ## Returns

  Returns a Map schema definition compatible with Drops.

  ## Examples

      # Basic inference
      Drops.Schema.Inference.infer_schema(MyApp.User, [])

      # Custom field presence
      Drops.Schema.Inference.infer_schema(MyApp.User,
        field_presence: %{name: :required, email: :required},
        default_presence: :optional
      )

      # Include all fields
      Drops.Schema.Inference.infer_schema(MyApp.User, exclude_fields: [])
  """
  def infer_schema(module, opts) when is_atom(module) do
    # Check if this is an Ecto schema module
    if ecto_schema_module?(module) do
      infer_ecto_schema(module, opts)
    else
      # For non-Ecto atoms, we can't infer a schema
      raise ArgumentError,
            "Cannot infer schema from atom #{inspect(module)} - not an Ecto schema module"
    end
  end

  # Private functions

  defp ecto_schema_module?(module) do
    Code.ensure_loaded?(module) and
      function_exported?(module, :__schema__, 1)
  end

  defp infer_ecto_schema(module, opts) do
    exclude_fields = Keyword.get(opts, :exclude_fields, [:id, :inserted_at, :updated_at])
    field_presence = Keyword.get(opts, :field_presence, %{})
    default_presence = Keyword.get(opts, :default_presence, :optional)

    # Get all fields from the Ecto schema
    all_fields = module.__schema__(:fields)

    # Filter out excluded fields
    fields = Enum.reject(all_fields, &(&1 in exclude_fields))

    # Convert each field to a Drops schema entry
    field_entries =
      Enum.map(fields, fn field ->
        field_type = module.__schema__(:type, field)
        drops_type = ecto_type_to_drops_type(field_type)

        # Determine field presence
        presence = Map.get(field_presence, field, default_presence)

        presence_key =
          case presence do
            :required -> required(field)
            :optional -> optional(field)
            # Default to optional for unknown values
            _ -> optional(field)
          end

        {presence_key, drops_type}
      end)

    # Return a map with the field entries
    Map.new(field_entries)
  end

  # Type mapping from Ecto types to Drops types
  defp ecto_type_to_drops_type(:string), do: string()
  defp ecto_type_to_drops_type(:integer), do: integer()
  defp ecto_type_to_drops_type(:float), do: float()
  defp ecto_type_to_drops_type(:boolean), do: boolean()
  # Could be enhanced with a decimal type
  defp ecto_type_to_drops_type(:decimal), do: any()
  # Could be enhanced with date validation
  defp ecto_type_to_drops_type(:date), do: any()
  # Could be enhanced with time validation
  defp ecto_type_to_drops_type(:time), do: any()
  defp ecto_type_to_drops_type(:naive_datetime), do: any()
  defp ecto_type_to_drops_type(:utc_datetime), do: any()
  defp ecto_type_to_drops_type(:binary), do: string()
  defp ecto_type_to_drops_type(:binary_id), do: string()
  defp ecto_type_to_drops_type(:id), do: integer()

  # Handle array types
  defp ecto_type_to_drops_type({:array, inner_type}) do
    list(ecto_type_to_drops_type(inner_type), [])
  end

  # Handle map types
  defp ecto_type_to_drops_type(:map), do: map()
  defp ecto_type_to_drops_type({:map, _}), do: map()

  # Fallback for unknown types
  defp ecto_type_to_drops_type(_), do: any()
end

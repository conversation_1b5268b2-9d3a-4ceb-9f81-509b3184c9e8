defmodule Drops.ContractCase do
  use ExUnit.CaseTemplate

  using do
    quote do
      alias Drops.Validator.Messages.DefaultBackend, as: MessageBackend

      import Drops.ContractCase

      def assert_errors(errors, {:error, messages}) do
        assert errors == Enum.map(messages, &to_string/1)
      end
    end
  end

  defmacro contract(do: body) do
    quote do
      setup(_) do
        defmodule TestContract do
          use Drops.Contract

          unquote(body)
        end

        on_exit(fn ->
          :code.purge(__MODULE__.TestContract)
          :code.delete(__MODULE__.TestContract)

          # Defined in doctests
          :code.purge(__MODULE__.UserContract)
          :code.delete(__MODULE__.UserContract)
        end)

        {:ok, contract: TestContract}
      end
    end
  end
end

defmodule Drops.DataCase do
  @moduledoc """
  This module defines the setup for tests requiring
  access to the application's data layer.

  You may define functions here to be used as helpers in
  your tests.

  Finally, if the test case interacts with the database,
  we enable the SQL sandbox, so changes done to the database
  are reverted at the end of every test. If you are using
  PostgreSQL, you can even run database tests asynchronously
  by setting `use Drops.DataCase, async: true`, although
  this option is not recommended for other databases.
  """

  use ExUnit.CaseTemplate

  using do
    quote do
      use Drops.ContractCase

      alias Drops.TestRepo

      import Ecto
      import Ecto.Changeset
      import Ecto.Query
      import Drops.DataCase
    end
  end

  setup tags do
    Drops.DataCase.setup_sandbox(tags)
    :ok
  end

  @doc """
  Sets up the sandbox based on the test tags.
  """
  def setup_sandbox(tags) do
    pid = Ecto.Adapters.SQL.Sandbox.start_owner!(Drops.TestRepo, shared: not tags[:async])
    on_exit(fn -> Ecto.Adapters.SQL.Sandbox.stop_owner(pid) end)
  end
end

# Setup Ecto for testing
if Mix.env() == :test do
  # Configure the test repository
  Application.put_env(:drops, Drops.TestRepo,
    adapter: Ecto.Adapters.SQLite3,
    database: ":memory:",
    pool: Ecto.Adapters.SQL.Sandbox,
    pool_size: 10,
    queue_target: 5000,
    queue_interval: 1000
  )

  # Configure Ecto repos
  Application.put_env(:drops, :ecto_repos, [Drops.TestRepo])

  # Load test support files
  Code.require_file("support/test_repo.ex", __DIR__)
  Code.require_file("support/ecto/user_schema.ex", __DIR__)

  # Start dependencies
  {:ok, _} = Application.ensure_all_started(:ecto_sql)
  {:ok, _} = Drops.TestRepo.start_link()

  # Set up the SQL sandbox
  Ecto.Adapters.SQL.Sandbox.mode(Drops.TestRepo, :manual)
end

ExUnit.start()

defmodule Drops.SchemaTest do
  use Drops.ContractCase

  alias Drops.Schema
  import Drops.Type.DSL

  describe "infer_and_compile/2" do
    test "infers and compiles Map schema" do
      schema_map = %{
        required(:name) => string(),
        required(:age) => integer()
      }

      result = Schema.infer_and_compile(schema_map, [])

      # Should return a compiled Drops.Types.Map struct
      assert %Drops.Types.Map{} = result
      assert length(result.keys) == 2

      # Check that the keys are properly compiled
      name_key = Enum.find(result.keys, &(&1.path == [:name]))
      age_key = Enum.find(result.keys, &(&1.path == [:age]))

      assert name_key.presence == :required
      assert age_key.presence == :required
      assert %Drops.Types.Primitive{primitive: :string} = name_key.type
      assert %Drops.Types.Primitive{primitive: :integer} = age_key.type
    end

    test "infers and compiles Ecto schema" do
      result = Schema.infer_and_compile(Test.Ecto.UserSchema, [])

      # Should return a compiled Drops.Types.Map struct
      assert %Drops.Types.Map{} = result
      assert length(result.keys) == 2

      # Check that the keys are properly compiled
      name_key = Enum.find(result.keys, &(&1.path == [:name]))
      email_key = Enum.find(result.keys, &(&1.path == [:email]))

      assert name_key.presence == :optional
      assert email_key.presence == :optional
      assert %Drops.Types.Primitive{primitive: :string} = name_key.type
      assert %Drops.Types.Primitive{primitive: :string} = email_key.type
    end

    test "passes options to inference and compilation" do
      result = Schema.infer_and_compile(Test.Ecto.UserSchema, 
        field_presence: %{name: :required},
        default_presence: :optional
      )

      # Check that options were applied
      name_key = Enum.find(result.keys, &(&1.path == [:name]))
      email_key = Enum.find(result.keys, &(&1.path == [:email]))

      assert name_key.presence == :required
      assert email_key.presence == :optional
    end
  end

  describe "compile_schema/3" do
    test "compiles schema AST using default compiler" do
      schema_ast = %{
        required(:name) => string(),
        required(:age) => integer()
      }

      result = Schema.compile_schema(%{}, schema_ast, [])

      assert %Drops.Types.Map{} = result
      assert length(result.keys) == 2
    end

    test "falls back to default compiler when no custom compiler exists" do
      # Use a type that doesn't have a custom compiler
      schema_ast = %{required(:test) => string()}
      
      result = Schema.compile_schema("some_string", schema_ast, [])

      assert %Drops.Types.Map{} = result
      assert length(result.keys) == 1
    end
  end

  describe "has_custom_compiler?/1" do
    test "returns false for types without custom compilers" do
      refute Schema.has_custom_compiler?(%{})
      refute Schema.has_custom_compiler?("string")
      refute Schema.has_custom_compiler?(123)
    end

    test "returns false for Ecto schemas (no custom compiler implemented yet)" do
      refute Schema.has_custom_compiler?(Test.Ecto.UserSchema)
    end
  end
end

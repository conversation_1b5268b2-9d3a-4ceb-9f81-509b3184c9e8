defmodule Drops.Schema.InferenceTest do
  use Drops.ContractCase

  alias Drops.Schema.Inference
  import Drops.Type.DSL

  describe "Map inference" do
    test "infers schema from a simple map" do
      schema_map = %{
        required(:name) => string(),
        required(:age) => integer()
      }

      result = Inference.infer_schema(schema_map, [])

      assert result == schema_map
    end

    test "infers schema from a complex map" do
      schema_map = %{
        required(:name) => string(:filled?),
        optional(:email) => string(),
        required(:address) => %{
          required(:street) => string(),
          required(:city) => string(),
          optional(:zipcode) => string()
        }
      }

      result = Inference.infer_schema(schema_map, [])

      assert result == schema_map
    end

    test "handles empty map" do
      schema_map = %{}

      result = Inference.infer_schema(schema_map, [])

      assert result == %{}
    end
  end

  describe "Ecto schema inference" do
    test "infers schema from Ecto schema module" do
      result = Inference.infer_schema(Test.Ecto.UserSchema, [])

      expected = %{
        optional(:name) => string(),
        optional(:email) => string()
      }

      assert result == expected
    end

    test "respects exclude_fields option" do
      result = Inference.infer_schema(Test.Ecto.UserSchema, exclude_fields: [:email])

      expected = %{
        optional(:name) => string()
      }

      assert result == expected
    end

    test "respects field_presence option" do
      result = Inference.infer_schema(Test.Ecto.UserSchema, 
        field_presence: %{name: :required, email: :optional}
      )

      expected = %{
        required(:name) => string(),
        optional(:email) => string()
      }

      assert result == expected
    end

    test "respects default_presence option" do
      result = Inference.infer_schema(Test.Ecto.UserSchema, 
        default_presence: :required
      )

      expected = %{
        required(:name) => string(),
        required(:email) => string()
      }

      assert result == expected
    end

    test "includes all fields when exclude_fields is empty" do
      result = Inference.infer_schema(Test.Ecto.UserSchema, exclude_fields: [])

      # Should include id, inserted_at, updated_at fields
      assert Map.has_key?(result, optional(:id))
      assert Map.has_key?(result, optional(:inserted_at))
      assert Map.has_key?(result, optional(:updated_at))
      assert Map.has_key?(result, optional(:name))
      assert Map.has_key?(result, optional(:email))
    end

    test "raises error for non-Ecto schema modules" do
      assert_raise ArgumentError, ~r/Cannot infer schema from atom/, fn ->
        Inference.infer_schema(NonExistentModule, [])
      end
    end

    test "raises error for non-schema atoms" do
      assert_raise ArgumentError, ~r/not an Ecto schema module/, fn ->
        Inference.infer_schema(String, [])
      end
    end
  end
end
